"""
Evaluation Framework for RL-QUBO

This module provides comprehensive evaluation tools to compare RL-enhanced FMQA
against traditional FMQA and other baseline methods on various benchmark problems.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from typing import Dict, List, Callable, Tuple, Any, Optional
from dataclasses import dataclass
import time
import logging
from pathlib import Path


@dataclass
class BenchmarkResult:
    """Results from a single benchmark run."""
    method_name: str
    problem_name: str
    best_value: float
    convergence_iteration: int
    total_evaluations: int
    runtime: float
    optimization_history: List[float]
    additional_metrics: Dict[str, Any]


class BenchmarkProblems:
    """Collection of benchmark optimization problems."""
    
    @staticmethod
    def quadratic_problem(d: int, seed: int = 42) -> Callable[[np.ndarray], float]:
        """
        Generate a quadratic optimization problem.
        
        Args:
            d: Problem dimension
            seed: Random seed
            
        Returns:
            Black-box function
        """
        rng = np.random.default_rng(seed)
        Q = rng.random((d, d))
        Q = (Q + Q.T) / 2
        Q = Q - np.mean(Q)
        
        def objective(x: np.ndarray) -> float:
            return x @ Q @ x
        
        return objective
    
    @staticmethod
    def ising_problem(d: int, seed: int = 42) -> Callable[[np.ndarray], float]:
        """
        Generate an Ising model optimization problem.
        
        Args:
            d: Problem dimension
            seed: Random seed
            
        Returns:
            Black-box function
        """
        rng = np.random.default_rng(seed)
        J = rng.normal(0, 1, (d, d))
        J = (J + J.T) / 2
        h = rng.normal(0, 0.5, d)
        
        def objective(x: np.ndarray) -> float:
            # Convert binary to spin variables
            s = 2 * x - 1
            return -0.5 * s @ J @ s - h @ s
        
        return objective
    
    @staticmethod
    def max_cut_problem(d: int, density: float = 0.5, seed: int = 42) -> Callable[[np.ndarray], float]:
        """
        Generate a Max-Cut optimization problem.
        
        Args:
            d: Problem dimension (number of nodes)
            density: Edge density
            seed: Random seed
            
        Returns:
            Black-box function
        """
        rng = np.random.default_rng(seed)
        
        # Generate random graph
        adj_matrix = np.zeros((d, d))
        for i in range(d):
            for j in range(i + 1, d):
                if rng.random() < density:
                    weight = rng.uniform(0.1, 1.0)
                    adj_matrix[i, j] = weight
                    adj_matrix[j, i] = weight
        
        def objective(x: np.ndarray) -> float:
            # Maximize cut (minimize negative cut)
            cut_value = 0
            for i in range(d):
                for j in range(i + 1, d):
                    if x[i] != x[j]:  # Different partitions
                        cut_value += adj_matrix[i, j]
            return -cut_value
        
        return objective
    
    @staticmethod
    def portfolio_optimization(d: int, seed: int = 42) -> Callable[[np.ndarray], float]:
        """
        Generate a portfolio optimization problem.
        
        Args:
            d: Number of assets
            seed: Random seed
            
        Returns:
            Black-box function
        """
        rng = np.random.default_rng(seed)
        
        # Generate expected returns and covariance matrix
        returns = rng.normal(0.1, 0.05, d)
        cov_matrix = rng.random((d, d))
        cov_matrix = cov_matrix @ cov_matrix.T / d  # Ensure positive definite
        
        risk_aversion = 2.0
        
        def objective(x: np.ndarray) -> float:
            if np.sum(x) == 0:
                return 1000  # Penalty for empty portfolio
            
            weights = x / np.sum(x)
            expected_return = weights @ returns
            risk = weights @ cov_matrix @ weights
            
            # Minimize negative utility (maximize utility)
            utility = expected_return - 0.5 * risk_aversion * risk
            return -utility
        
        return objective


class EvaluationFramework:
    """
    Comprehensive evaluation framework for RL-QUBO methods.
    
    Provides tools for benchmarking, comparison, and analysis of different
    optimization approaches on various problem types.
    """
    
    def __init__(self, output_dir: str = "evaluation_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # Results storage
        self.benchmark_results: List[BenchmarkResult] = []
        
        # Plotting configuration
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def run_benchmark(
        self,
        method_func: Callable,
        problem_func: Callable[[np.ndarray], float],
        problem_name: str,
        method_name: str,
        problem_dim: int,
        n_iterations: int,
        n_initial: int = 10,
        **method_kwargs
    ) -> BenchmarkResult:
        """
        Run a single benchmark experiment.
        
        Args:
            method_func: Optimization method to test
            problem_func: Black-box function to optimize
            problem_name: Name of the problem
            method_name: Name of the method
            problem_dim: Problem dimension
            n_iterations: Number of optimization iterations
            n_initial: Number of initial data points
            **method_kwargs: Additional arguments for the method
            
        Returns:
            Benchmark result
        """
        self.logger.info(f"Running benchmark: {method_name} on {problem_name}")
        
        start_time = time.time()
        
        try:
            # Run the optimization method
            result = method_func(
                problem_func=problem_func,
                problem_dim=problem_dim,
                n_iterations=n_iterations,
                n_initial=n_initial,
                **method_kwargs
            )
            
            runtime = time.time() - start_time
            
            # Extract results
            best_value = result.get('best_value', float('inf'))
            optimization_history = result.get('optimization_history', [])
            
            # Find convergence iteration (when improvement becomes minimal)
            convergence_iteration = len(optimization_history)
            if len(optimization_history) > 5:
                for i in range(5, len(optimization_history)):
                    recent_improvement = optimization_history[i-5] - optimization_history[i]
                    if recent_improvement < 1e-6:
                        convergence_iteration = i
                        break
            
            benchmark_result = BenchmarkResult(
                method_name=method_name,
                problem_name=problem_name,
                best_value=best_value,
                convergence_iteration=convergence_iteration,
                total_evaluations=result.get('total_evaluations', n_initial + n_iterations),
                runtime=runtime,
                optimization_history=optimization_history,
                additional_metrics=result.get('additional_metrics', {})
            )
            
            self.benchmark_results.append(benchmark_result)
            self.logger.info(f"Benchmark completed: {method_name} achieved {best_value:.4f}")
            
            return benchmark_result
            
        except Exception as e:
            self.logger.error(f"Benchmark failed: {method_name} on {problem_name}: {str(e)}")
            raise
    
    def compare_methods(
        self,
        methods: Dict[str, Callable],
        problems: Dict[str, Callable],
        problem_dims: List[int] = [50, 100],
        n_iterations: int = 50,
        n_runs: int = 5,
        n_initial: int = 10
    ) -> pd.DataFrame:
        """
        Compare multiple methods across multiple problems.
        
        Args:
            methods: Dictionary of method names to method functions
            problems: Dictionary of problem names to problem generators
            problem_dims: List of problem dimensions to test
            n_iterations: Number of optimization iterations
            n_runs: Number of independent runs per configuration
            n_initial: Number of initial data points
            
        Returns:
            DataFrame with comparison results
        """
        results = []
        
        total_experiments = len(methods) * len(problems) * len(problem_dims) * n_runs
        experiment_count = 0
        
        for method_name, method_func in methods.items():
            for problem_name, problem_generator in problems.items():
                for dim in problem_dims:
                    for run in range(n_runs):
                        experiment_count += 1
                        self.logger.info(
                            f"Experiment {experiment_count}/{total_experiments}: "
                            f"{method_name} on {problem_name} (dim={dim}, run={run+1})"
                        )
                        
                        # Generate problem instance
                        problem_func = problem_generator(dim, seed=42 + run)
                        
                        # Run benchmark
                        try:
                            result = self.run_benchmark(
                                method_func=method_func,
                                problem_func=problem_func,
                                problem_name=f"{problem_name}_dim{dim}",
                                method_name=method_name,
                                problem_dim=dim,
                                n_iterations=n_iterations,
                                n_initial=n_initial
                            )
                            
                            results.append({
                                'method': method_name,
                                'problem': problem_name,
                                'dimension': dim,
                                'run': run,
                                'best_value': result.best_value,
                                'convergence_iteration': result.convergence_iteration,
                                'runtime': result.runtime,
                                'total_evaluations': result.total_evaluations
                            })
                            
                        except Exception as e:
                            self.logger.error(f"Failed experiment: {str(e)}")
                            results.append({
                                'method': method_name,
                                'problem': problem_name,
                                'dimension': dim,
                                'run': run,
                                'best_value': float('inf'),
                                'convergence_iteration': n_iterations,
                                'runtime': float('inf'),
                                'total_evaluations': n_initial + n_iterations
                            })
        
        return pd.DataFrame(results)
    
    def plot_convergence_comparison(
        self,
        results_df: pd.DataFrame,
        save_path: Optional[str] = None
    ) -> None:
        """Plot convergence comparison across methods and problems."""
        
        # Get unique problems and methods
        problems = results_df['problem'].unique()
        methods = results_df['method'].unique()
        
        fig, axes = plt.subplots(1, len(problems), figsize=(5 * len(problems), 4))
        if len(problems) == 1:
            axes = [axes]
        
        for i, problem in enumerate(problems):
            ax = axes[i]
            
            for method in methods:
                method_data = results_df[
                    (results_df['problem'] == problem) & 
                    (results_df['method'] == method)
                ]
                
                if not method_data.empty:
                    # Plot mean convergence with confidence intervals
                    convergence_values = method_data['convergence_iteration'].values
                    mean_conv = np.mean(convergence_values)
                    std_conv = np.std(convergence_values)
                    
                    ax.bar(method, mean_conv, yerr=std_conv, alpha=0.7, label=method)
            
            ax.set_title(f'Convergence Speed - {problem}')
            ax.set_ylabel('Iterations to Convergence')
            ax.set_xlabel('Method')
            ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_performance_comparison(
        self,
        results_df: pd.DataFrame,
        save_path: Optional[str] = None
    ) -> None:
        """Plot performance comparison across methods and problems."""
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Best value comparison
        sns.boxplot(data=results_df, x='method', y='best_value', hue='problem', ax=ax1)
        ax1.set_title('Best Value Achieved')
        ax1.set_ylabel('Objective Value')
        ax1.tick_params(axis='x', rotation=45)
        
        # Runtime comparison
        sns.boxplot(data=results_df, x='method', y='runtime', hue='problem', ax=ax2)
        ax2.set_title('Runtime Comparison')
        ax2.set_ylabel('Runtime (seconds)')
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self, results_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive evaluation report."""
        
        report = {
            'summary_statistics': {},
            'method_rankings': {},
            'problem_analysis': {},
            'statistical_tests': {}
        }
        
        # Summary statistics
        summary_stats = results_df.groupby(['method', 'problem']).agg({
            'best_value': ['mean', 'std', 'min'],
            'convergence_iteration': ['mean', 'std'],
            'runtime': ['mean', 'std']
        }).round(4)
        
        report['summary_statistics'] = summary_stats.to_dict()
        
        # Method rankings by problem
        for problem in results_df['problem'].unique():
            problem_data = results_df[results_df['problem'] == problem]
            method_performance = problem_data.groupby('method')['best_value'].mean().sort_values()
            report['method_rankings'][problem] = method_performance.to_dict()
        
        # Overall method ranking
        overall_ranking = results_df.groupby('method')['best_value'].mean().sort_values()
        report['method_rankings']['overall'] = overall_ranking.to_dict()
        
        return report
    
    def save_results(self, results_df: pd.DataFrame, filename: str = "benchmark_results.csv") -> None:
        """Save benchmark results to file."""
        filepath = self.output_dir / filename
        results_df.to_csv(filepath, index=False)
        self.logger.info(f"Results saved to {filepath}")
    
    def load_results(self, filename: str = "benchmark_results.csv") -> pd.DataFrame:
        """Load benchmark results from file."""
        filepath = self.output_dir / filename
        return pd.read_csv(filepath)
