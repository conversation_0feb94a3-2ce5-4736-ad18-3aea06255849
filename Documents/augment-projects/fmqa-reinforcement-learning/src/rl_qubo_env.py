"""
Reinforcement Learning Environment for QUBO Model Construction

This module implements a gymnasium environment where an RL agent learns to 
construct alternative QUBO formulations by dynamically adjusting the 
exploration-exploitation trade-off parameter β in the RL-QUBO algorithm.
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Tuple, Any, Optional, List
import torch
from dataclasses import dataclass


@dataclass
class OptimizationState:
    """Represents the current state of the optimization process."""
    iteration: int
    dataset_size: int
    best_value: float
    current_value: float
    improvement_rate: float
    exploration_history: List[float]
    exploitation_history: List[float]
    convergence_metric: float


class RLQUBOEnvironment(gym.Env):
    """
    RL Environment for learning optimal QUBO construction strategies.
    
    State Space:
    - Current optimization iteration
    - Dataset size and statistics
    - Best objective value found so far
    - Recent improvement rate
    - Exploration/exploitation history
    - Convergence metrics
    
    Action Space:
    - Continuous value for β parameter (exploration-exploitation trade-off)
    
    Reward Function:
    - Primary: Improvement in best objective value
    - Secondary: Convergence speed bonus
    - Penalty: Excessive exploration without improvement
    """
    
    def __init__(
        self,
        problem_dim: int = 100,
        max_iterations: int = 50,
        initial_dataset_size: int = 10,
        beta_range: Tuple[float, float] = (0.0, 2.0),
        reward_scale: float = 1.0,
        convergence_threshold: float = 1e-6,
        history_length: int = 5
    ):
        super().__init__()
        
        self.problem_dim = problem_dim
        self.max_iterations = max_iterations
        self.initial_dataset_size = initial_dataset_size
        self.beta_range = beta_range
        self.reward_scale = reward_scale
        self.convergence_threshold = convergence_threshold
        self.history_length = history_length
        
        # Action space: continuous β parameter
        self.action_space = spaces.Box(
            low=beta_range[0], 
            high=beta_range[1], 
            shape=(1,), 
            dtype=np.float32
        )
        
        # State space: [iteration, dataset_size, best_value, current_value, 
        #               improvement_rate, convergence_metric, exploration_history, exploitation_history]
        state_dim = 6 + 2 * history_length
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(state_dim,), 
            dtype=np.float32
        )
        
        # Initialize state
        self.reset()
    
    def reset(
        self, 
        seed: Optional[int] = None, 
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset the environment to initial state."""
        super().reset(seed=seed)
        
        self.current_iteration = 0
        self.dataset_size = self.initial_dataset_size
        self.best_value = np.inf
        self.current_value = np.inf
        self.previous_best = np.inf
        self.improvement_rate = 0.0
        self.convergence_metric = 1.0
        
        # History tracking
        self.exploration_history = [0.0] * self.history_length
        self.exploitation_history = [0.0] * self.history_length
        self.value_history = []
        self.beta_history = []
        
        # Episode tracking
        self.episode_reward = 0.0
        self.episode_improvements = 0
        
        state = self._get_state()
        info = self._get_info()
        
        return state, info
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """
        Execute one step in the environment.
        
        Args:
            action: β parameter value for current iteration
            
        Returns:
            observation: Next state
            reward: Reward for this step
            terminated: Whether episode is finished
            truncated: Whether episode was truncated
            info: Additional information
        """
        beta = float(action[0])
        beta = np.clip(beta, self.beta_range[0], self.beta_range[1])
        
        # Simulate FMQA iteration with given β
        improvement, exploration_score, exploitation_score = self._simulate_fmqa_iteration(beta)
        
        # Update state
        self.current_iteration += 1
        self.dataset_size += 1
        self.previous_best = self.best_value
        
        if improvement > 0:
            self.best_value -= improvement
            self.episode_improvements += 1
        
        # Update histories
        self.exploration_history.pop(0)
        self.exploration_history.append(exploration_score)
        self.exploitation_history.pop(0)
        self.exploitation_history.append(exploitation_score)
        self.value_history.append(self.best_value)
        self.beta_history.append(beta)
        
        # Calculate improvement rate and convergence
        self._update_metrics()
        
        # Calculate reward
        reward = self._calculate_reward(improvement, beta, exploration_score, exploitation_score)
        self.episode_reward += reward
        
        # Check termination conditions
        terminated = self.current_iteration >= self.max_iterations
        truncated = False
        
        # Check early convergence
        if len(self.value_history) >= 10:
            recent_variance = np.var(self.value_history[-10:])
            if recent_variance < self.convergence_threshold:
                terminated = True
        
        state = self._get_state()
        info = self._get_info()
        
        return state, reward, terminated, truncated, info
    
    def _simulate_fmqa_iteration(self, beta: float) -> Tuple[float, float, float]:
        """
        Simulate one FMQA iteration with given β parameter.
        
        Returns:
            improvement: Objective function improvement
            exploration_score: How much exploration was done
            exploitation_score: How much exploitation was done
        """
        # Simplified simulation - in practice this would call actual FMQA
        
        # Base improvement potential decreases over time
        base_improvement = max(0, 10.0 * np.exp(-0.1 * self.current_iteration))
        
        # β controls exploration vs exploitation trade-off
        exploration_factor = beta / self.beta_range[1]
        exploitation_factor = 1.0 - exploration_factor
        
        # Exploration helps early, exploitation helps later
        exploration_benefit = exploration_factor * max(0, 1.0 - self.current_iteration / 20.0)
        exploitation_benefit = exploitation_factor * min(1.0, self.current_iteration / 10.0)
        
        # Add noise to simulate stochastic optimization
        noise = np.random.normal(0, 0.1)
        
        improvement = base_improvement * (exploration_benefit + exploitation_benefit) + noise
        improvement = max(0, improvement)  # No negative improvements
        
        exploration_score = exploration_factor
        exploitation_score = exploitation_factor
        
        return improvement, exploration_score, exploitation_score
    
    def _update_metrics(self):
        """Update improvement rate and convergence metrics."""
        if len(self.value_history) >= 2:
            recent_improvement = self.value_history[-2] - self.value_history[-1]
            self.improvement_rate = recent_improvement
        
        # Convergence metric based on recent value variance
        if len(self.value_history) >= 5:
            recent_values = self.value_history[-5:]
            self.convergence_metric = np.var(recent_values)
        else:
            self.convergence_metric = 1.0
    
    def _calculate_reward(
        self, 
        improvement: float, 
        beta: float, 
        exploration_score: float, 
        exploitation_score: float
    ) -> float:
        """Calculate reward for current step."""
        
        # Primary reward: objective improvement
        improvement_reward = improvement * self.reward_scale
        
        # Convergence speed bonus
        convergence_bonus = 0.0
        if self.current_iteration > 5:
            avg_improvement = np.mean([max(0, self.value_history[i-1] - self.value_history[i]) 
                                     for i in range(1, min(6, len(self.value_history)))])
            if avg_improvement > 0:
                convergence_bonus = 0.1 * avg_improvement
        
        # Balanced exploration-exploitation bonus
        balance_bonus = 0.0
        if 0.3 <= exploration_score <= 0.7:  # Reward balanced strategies
            balance_bonus = 0.05
        
        # Penalty for excessive exploration without improvement
        exploration_penalty = 0.0
        if exploration_score > 0.8 and improvement < 0.01:
            exploration_penalty = -0.1
        
        total_reward = improvement_reward + convergence_bonus + balance_bonus + exploration_penalty
        
        return total_reward
    
    def _get_state(self) -> np.ndarray:
        """Get current state representation."""
        state = [
            self.current_iteration / self.max_iterations,  # Normalized iteration
            self.dataset_size / (self.initial_dataset_size + self.max_iterations),  # Normalized dataset size
            self.best_value if self.best_value != np.inf else 0.0,  # Best value found
            self.current_value if self.current_value != np.inf else 0.0,  # Current value
            self.improvement_rate,  # Recent improvement rate
            self.convergence_metric,  # Convergence metric
        ]
        
        # Add exploration and exploitation histories
        state.extend(self.exploration_history)
        state.extend(self.exploitation_history)
        
        return np.array(state, dtype=np.float32)
    
    def _get_info(self) -> Dict[str, Any]:
        """Get additional information about current state."""
        return {
            "iteration": self.current_iteration,
            "dataset_size": self.dataset_size,
            "best_value": self.best_value,
            "improvement_rate": self.improvement_rate,
            "convergence_metric": self.convergence_metric,
            "episode_reward": self.episode_reward,
            "episode_improvements": self.episode_improvements,
            "beta_history": self.beta_history.copy(),
            "value_history": self.value_history.copy()
        }
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment state."""
        if mode == "human":
            print(f"Iteration: {self.current_iteration}/{self.max_iterations}")
            print(f"Best value: {self.best_value:.4f}")
            print(f"Dataset size: {self.dataset_size}")
            print(f"Improvement rate: {self.improvement_rate:.4f}")
            print(f"Convergence metric: {self.convergence_metric:.4f}")
            print("-" * 40)
        
        return None
