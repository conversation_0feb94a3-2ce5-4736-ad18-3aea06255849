# RL-QUBO: Reinforcement Learning for QUBO Model Construction
"""
A reinforcement learning framework for constructing alternative QUBO 
(Quadratic Unconstrained Binary Optimization) models that enhance the 
traditional FMQA (Factorization Machine with Quantum Annealing) approach.

This package provides:
- RL environments for QUBO optimization
- Policy gradient agents for learning exploration-exploitation trade-offs
- Enhanced FMQA algorithms with RL-guided parameter adaptation
- Evaluation frameworks and benchmark problems
"""

__version__ = "0.1.0"
__author__ = "RL-QUBO Development Team"

from .rl_qubo_env import RLQUBOEnvironment
from .rl_agent import RLAgent
from .rl_fmqa import RLEnhancedFMQA
from .evaluation import EvaluationFramework

__all__ = [
    "RLQUBOEnvironment",
    "RLAgent", 
    "RLEnhancedFMQA",
    "EvaluationFramework"
]
