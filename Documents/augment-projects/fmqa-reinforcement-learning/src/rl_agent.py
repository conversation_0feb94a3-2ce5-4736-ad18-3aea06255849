"""
Reinforcement Learning Agent for QUBO Parameter Optimization

This module implements a policy gradient agent (PPO) that learns to optimize
the exploration-exploitation trade-off parameter β in the RL-QUBO algorithm.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.distributions import Normal
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import gymnasium as gym
from stable_baselines3 import PPO
from stable_baselines3.common.policies import ActorCriticPolicy
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
import logging


@dataclass
class TrainingConfig:
    """Configuration for RL agent training."""
    learning_rate: float = 3e-4
    n_steps: int = 2048
    batch_size: int = 64
    n_epochs: int = 10
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_range: float = 0.2
    ent_coef: float = 0.01
    vf_coef: float = 0.5
    max_grad_norm: float = 0.5
    target_kl: Optional[float] = None


class QUBOFeatureExtractor(BaseFeaturesExtractor):
    """
    Custom feature extractor for QUBO optimization states.
    
    Processes the state vector to extract relevant features for policy learning.
    """
    
    def __init__(self, observation_space: gym.Space, features_dim: int = 128):
        super().__init__(observation_space, features_dim)
        
        input_dim = observation_space.shape[0]
        
        self.feature_net = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, features_dim),
            nn.ReLU()
        )
    
    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        return self.feature_net(observations)


class CustomActorCriticPolicy(ActorCriticPolicy):
    """
    Custom Actor-Critic policy with specialized feature extraction for QUBO problems.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(
            *args,
            **kwargs,
            features_extractor_class=QUBOFeatureExtractor,
            features_extractor_kwargs=dict(features_dim=128),
        )


class RLAgent:
    """
    Reinforcement Learning Agent for QUBO parameter optimization.
    
    Uses Proximal Policy Optimization (PPO) to learn optimal β parameter
    selection strategies for the RL-QUBO algorithm.
    """
    
    def __init__(
        self,
        env: gym.Env,
        config: Optional[TrainingConfig] = None,
        device: str = "auto",
        verbose: int = 1,
        tensorboard_log: Optional[str] = None
    ):
        self.env = env
        self.config = config or TrainingConfig()
        self.device = device
        self.verbose = verbose
        self.tensorboard_log = tensorboard_log
        
        # Initialize PPO agent
        self.agent = PPO(
            CustomActorCriticPolicy,
            env,
            learning_rate=self.config.learning_rate,
            n_steps=self.config.n_steps,
            batch_size=self.config.batch_size,
            n_epochs=self.config.n_epochs,
            gamma=self.config.gamma,
            gae_lambda=self.config.gae_lambda,
            clip_range=self.config.clip_range,
            ent_coef=self.config.ent_coef,
            vf_coef=self.config.vf_coef,
            max_grad_norm=self.config.max_grad_norm,
            target_kl=self.config.target_kl,
            device=device,
            verbose=verbose,
            tensorboard_log=tensorboard_log
        )
        
        # Training metrics
        self.training_history = {
            "episode_rewards": [],
            "episode_lengths": [],
            "policy_losses": [],
            "value_losses": [],
            "entropy_losses": []
        }
        
        self.logger = logging.getLogger(__name__)
    
    def train(
        self,
        total_timesteps: int,
        callback=None,
        log_interval: int = 10,
        eval_env: Optional[gym.Env] = None,
        eval_freq: int = 10000,
        n_eval_episodes: int = 5
    ) -> "RLAgent":
        """
        Train the RL agent.
        
        Args:
            total_timesteps: Total number of timesteps to train
            callback: Optional callback for training monitoring
            log_interval: Logging interval
            eval_env: Environment for evaluation
            eval_freq: Evaluation frequency
            n_eval_episodes: Number of episodes for evaluation
            
        Returns:
            Self for method chaining
        """
        self.logger.info(f"Starting training for {total_timesteps} timesteps")
        
        # Setup evaluation callback if eval_env provided
        eval_callback = None
        if eval_env is not None:
            from stable_baselines3.common.callbacks import EvalCallback
            eval_callback = EvalCallback(
                eval_env,
                best_model_save_path=None,
                log_path=None,
                eval_freq=eval_freq,
                n_eval_episodes=n_eval_episodes,
                deterministic=True,
                render=False
            )
        
        # Train the agent
        self.agent.learn(
            total_timesteps=total_timesteps,
            callback=eval_callback or callback,
            log_interval=log_interval,
            progress_bar=True
        )
        
        self.logger.info("Training completed")
        return self
    
    def predict(
        self,
        observation: np.ndarray,
        deterministic: bool = True
    ) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        Predict action for given observation.
        
        Args:
            observation: Current state observation
            deterministic: Whether to use deterministic policy
            
        Returns:
            action: Predicted action
            state: Internal state (for recurrent policies)
        """
        return self.agent.predict(observation, deterministic=deterministic)
    
    def evaluate(
        self,
        eval_env: gym.Env,
        n_episodes: int = 10,
        deterministic: bool = True,
        render: bool = False
    ) -> Dict[str, float]:
        """
        Evaluate the agent's performance.
        
        Args:
            eval_env: Environment for evaluation
            n_episodes: Number of episodes to evaluate
            deterministic: Whether to use deterministic policy
            render: Whether to render episodes
            
        Returns:
            Dictionary with evaluation metrics
        """
        from stable_baselines3.common.evaluation import evaluate_policy
        
        mean_reward, std_reward = evaluate_policy(
            self.agent,
            eval_env,
            n_eval_episodes=n_episodes,
            deterministic=deterministic,
            render=render,
            return_episode_rewards=False
        )
        
        return {
            "mean_reward": mean_reward,
            "std_reward": std_reward,
            "n_episodes": n_episodes
        }
    
    def save(self, path: str) -> None:
        """Save the trained agent."""
        self.agent.save(path)
        self.logger.info(f"Agent saved to {path}")
    
    def load(self, path: str) -> "RLAgent":
        """Load a trained agent."""
        self.agent = PPO.load(path, env=self.env)
        self.logger.info(f"Agent loaded from {path}")
        return self
    
    def get_action_distribution(self, observation: np.ndarray) -> Dict[str, Any]:
        """
        Get the action distribution for given observation.
        
        Args:
            observation: Current state observation
            
        Returns:
            Dictionary with distribution parameters
        """
        obs_tensor = torch.as_tensor(observation).float()
        if len(obs_tensor.shape) == 1:
            obs_tensor = obs_tensor.unsqueeze(0)
        
        with torch.no_grad():
            features = self.agent.policy.extract_features(obs_tensor)
            latent_pi = self.agent.policy.mlp_extractor.forward_actor(features)
            mean_actions = self.agent.policy.action_net(latent_pi)
            
            if hasattr(self.agent.policy, 'log_std'):
                log_std = self.agent.policy.log_std
                std = torch.exp(log_std)
            else:
                std = torch.ones_like(mean_actions)
        
        return {
            "mean": mean_actions.cpu().numpy(),
            "std": std.cpu().numpy(),
            "distribution_type": "normal"
        }
    
    def analyze_policy(self, states: List[np.ndarray]) -> Dict[str, Any]:
        """
        Analyze the learned policy across different states.
        
        Args:
            states: List of state observations to analyze
            
        Returns:
            Dictionary with policy analysis results
        """
        actions = []
        distributions = []
        
        for state in states:
            action, _ = self.predict(state, deterministic=False)
            dist = self.get_action_distribution(state)
            
            actions.append(action[0])
            distributions.append(dist)
        
        return {
            "states": states,
            "actions": actions,
            "distributions": distributions,
            "action_mean": np.mean(actions),
            "action_std": np.std(actions),
            "action_range": (np.min(actions), np.max(actions))
        }
    
    def get_training_metrics(self) -> Dict[str, List[float]]:
        """Get training metrics history."""
        return self.training_history.copy()
    
    def reset_training_metrics(self) -> None:
        """Reset training metrics history."""
        for key in self.training_history:
            self.training_history[key] = []
