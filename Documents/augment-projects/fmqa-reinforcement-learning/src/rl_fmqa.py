"""
RL-Enhanced FMQA Algorithm

This module implements the RL-QUBO method that enhances traditional FMQA
by incorporating reinforcement learning to dynamically adjust the 
exploration-exploitation trade-off parameter β.
"""

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader, random_split
from tqdm.auto import tqdm, trange
import copy
from typing import Callable, Tuple, List, Dict, Any, Optional
from dataclasses import dataclass
import logging

# Amplify imports (would be used in real implementation)
try:
    from amplify import VariableGenerator, Model, solve, Poly, FixstarsClient
    AMPLIFY_AVAILABLE = True
except ImportError:
    AMPLIFY_AVAILABLE = False
    logging.warning("Amplify SDK not available. Using mock implementation.")


@dataclass
class FMQAConfig:
    """Configuration for FMQA algorithm."""
    k: int = 10  # FM hyperparameter
    epochs: int = 2000
    learning_rate: float = 0.1
    batch_size: int = 8
    train_split: float = 0.8
    timeout_ms: int = 2000


class TorchFM(nn.Module):
    """
    Factorization Machine model implemented in PyTorch.
    
    This is the same FM model used in traditional FMQA, but enhanced
    to work with the RL-QUBO framework.
    """
    
    def __init__(self, d: int, k: int):
        """
        Initialize Factorization Machine model.
        
        Args:
            d: Input vector size
            k: Factorization parameter
        """
        super().__init__()
        self.d = d
        self.k = k
        self.v = torch.randn((d, k), requires_grad=True)
        self.w = torch.randn((d,), requires_grad=True)
        self.w0 = torch.randn((), requires_grad=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass of FM model."""
        out_linear = torch.matmul(x, self.w) + self.w0
        
        out_1 = torch.matmul(x, self.v).pow(2).sum(1)
        out_2 = torch.matmul(x.pow(2), self.v.pow(2)).sum(1)
        out_quadratic = 0.5 * (out_1 - out_2)
        
        return out_linear + out_quadratic
    
    def get_parameters(self) -> Tuple[np.ndarray, np.ndarray, float]:
        """Get model parameters for QUBO construction."""
        np_v = self.v.detach().numpy().copy()
        np_w = self.w.detach().numpy().copy()
        np_w0 = self.w0.detach().numpy().copy()
        return np_v, np_w, float(np_w0)


class RLEnhancedFMQA:
    """
    RL-Enhanced FMQA Algorithm.
    
    This class implements the RL-QUBO method that combines traditional FMQA
    with reinforcement learning to learn optimal exploration-exploitation
    trade-offs dynamically.
    """
    
    def __init__(
        self,
        problem_dim: int,
        blackbox_func: Callable[[np.ndarray], float],
        config: Optional[FMQAConfig] = None,
        client=None,
        seed: int = 1234
    ):
        """
        Initialize RL-Enhanced FMQA.
        
        Args:
            problem_dim: Dimension of the optimization problem
            blackbox_func: Black-box function to optimize
            config: FMQA configuration
            client: Amplify client for quantum annealing
            seed: Random seed
        """
        self.problem_dim = problem_dim
        self.blackbox_func = blackbox_func
        self.config = config or FMQAConfig()
        self.client = client
        self.seed = seed
        
        # Set random seeds
        np.random.seed(seed)
        torch.manual_seed(seed)
        self.rng = np.random.default_rng(seed)
        
        # Initialize data storage
        self.x_data = None
        self.y_data = None
        self.iteration = 0
        
        # Metrics tracking
        self.optimization_history = []
        self.beta_history = []
        self.improvement_history = []
        self.exploration_scores = []
        self.exploitation_scores = []
        
        self.logger = logging.getLogger(__name__)
    
    def initialize_dataset(self, n_initial: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create initial training dataset.
        
        Args:
            n_initial: Number of initial data points
            
        Returns:
            Initial x and y data
        """
        self.logger.info(f"Initializing dataset with {n_initial} points")
        
        # Generate random binary vectors
        x_init = self.rng.choice([0, 1], size=(n_initial, self.problem_dim))
        
        # Ensure uniqueness
        x_init = np.unique(x_init, axis=0)
        while x_init.shape[0] < n_initial:
            additional = self.rng.choice([0, 1], size=(n_initial - x_init.shape[0], self.problem_dim))
            x_init = np.vstack([x_init, additional])
            x_init = np.unique(x_init, axis=0)
        
        # Evaluate black-box function
        y_init = np.array([self.blackbox_func(x) for x in x_init])
        
        self.x_data = x_init
        self.y_data = y_init
        
        return x_init, y_init
    
    def train_fm_model(self, x: np.ndarray, y: np.ndarray) -> TorchFM:
        """
        Train Factorization Machine model.
        
        Args:
            x: Input data
            y: Target values
            
        Returns:
            Trained FM model
        """
        model = TorchFM(self.problem_dim, self.config.k)
        
        # Setup training
        optimizer = torch.optim.AdamW(
            [model.v, model.w, model.w0], 
            lr=self.config.learning_rate
        )
        loss_func = nn.MSELoss()
        
        # Prepare data
        x_tensor = torch.from_numpy(x).float()
        y_tensor = torch.from_numpy(y).float()
        dataset = TensorDataset(x_tensor, y_tensor)
        
        train_set, valid_set = random_split(dataset, [self.config.train_split, 1 - self.config.train_split])
        train_loader = DataLoader(train_set, batch_size=self.config.batch_size, shuffle=True)
        valid_loader = DataLoader(valid_set, batch_size=self.config.batch_size, shuffle=False)
        
        # Training loop
        min_loss = float('inf')
        best_state = model.state_dict()
        
        for _ in trange(self.config.epochs, leave=False, desc="Training FM"):
            # Training phase
            model.train()
            for x_batch, y_batch in train_loader:
                optimizer.zero_grad()
                pred_y = model(x_batch)
                loss = loss_func(pred_y, y_batch)
                loss.backward()
                optimizer.step()
            
            # Validation phase
            model.eval()
            with torch.no_grad():
                val_loss = 0
                for x_val, y_val in valid_loader:
                    pred_val = model(x_val)
                    val_loss += loss_func(pred_val, y_val).item()
                
                if val_loss < min_loss:
                    min_loss = val_loss
                    best_state = copy.deepcopy(model.state_dict())
        
        # Load best model
        model.load_state_dict(best_state)
        return model
    
    def construct_rl_qubo(
        self, 
        fm_model: TorchFM, 
        beta: float, 
        x_history: np.ndarray
    ) -> Tuple[np.ndarray, float, float]:
        """
        Construct RL-enhanced QUBO with exploration term.
        
        Args:
            fm_model: Trained FM model
            beta: Exploration-exploitation trade-off parameter
            x_history: Historical data points for exploration term
            
        Returns:
            Optimal x, exploration score, exploitation score
        """
        v, w, w0 = fm_model.get_parameters()
        
        # Calculate exploration term (Hamming distance to historical points)
        def calculate_exploration_term(x_candidate: np.ndarray) -> float:
            if len(x_history) == 0:
                return 0.0
            
            hamming_distances = np.sum(np.abs(x_candidate - x_history), axis=1)
            return np.sum(hamming_distances)
        
        # For simplicity, use a heuristic optimization instead of quantum annealing
        # In practice, this would construct and solve the actual QUBO
        best_x = None
        best_value = float('inf')
        
        # Sample multiple candidates and select best
        n_candidates = 1000
        for _ in range(n_candidates):
            x_candidate = self.rng.choice([0, 1], size=self.problem_dim)
            
            # Calculate FM prediction (exploitation term)
            x_tensor = torch.from_numpy(x_candidate.astype(float)).float()
            with torch.no_grad():
                fm_value = fm_model(x_tensor.unsqueeze(0)).item()
            
            # Calculate exploration term
            exploration_value = calculate_exploration_term(x_candidate)
            
            # Combined objective: exploitation - beta * exploration
            combined_value = fm_value - beta * exploration_value
            
            if combined_value < best_value:
                best_value = combined_value
                best_x = x_candidate
        
        # Calculate scores
        exploration_score = beta / (1.0 + beta)  # Normalized exploration emphasis
        exploitation_score = 1.0 / (1.0 + beta)  # Normalized exploitation emphasis
        
        return best_x, exploration_score, exploitation_score
    
    def optimize_step(self, beta: float) -> Dict[str, Any]:
        """
        Perform one optimization step with given beta.
        
        Args:
            beta: Exploration-exploitation trade-off parameter
            
        Returns:
            Step results including improvement and scores
        """
        # Train FM model on current data
        fm_model = self.train_fm_model(self.x_data, self.y_data)
        
        # Construct and solve RL-QUBO
        x_new, exploration_score, exploitation_score = self.construct_rl_qubo(
            fm_model, beta, self.x_data
        )
        
        # Ensure no duplicates
        while np.any(np.all(x_new == self.x_data, axis=1)):
            flip_idx = self.rng.choice(self.problem_dim)
            x_new[flip_idx] = 1 - x_new[flip_idx]
        
        # Evaluate black-box function
        y_new = self.blackbox_func(x_new)
        
        # Update dataset
        self.x_data = np.vstack([self.x_data, x_new])
        self.y_data = np.append(self.y_data, y_new)
        
        # Calculate improvement
        previous_best = np.min(self.y_data[:-1])
        current_best = np.min(self.y_data)
        improvement = max(0, previous_best - current_best)
        
        # Update tracking
        self.iteration += 1
        self.optimization_history.append(current_best)
        self.beta_history.append(beta)
        self.improvement_history.append(improvement)
        self.exploration_scores.append(exploration_score)
        self.exploitation_scores.append(exploitation_score)
        
        return {
            "iteration": self.iteration,
            "x_new": x_new,
            "y_new": y_new,
            "improvement": improvement,
            "best_value": current_best,
            "exploration_score": exploration_score,
            "exploitation_score": exploitation_score,
            "beta": beta
        }
    
    def get_state_representation(self) -> np.ndarray:
        """
        Get current state representation for RL agent.
        
        Returns:
            State vector for RL environment
        """
        if len(self.optimization_history) == 0:
            return np.zeros(16)  # Default state size
        
        # Basic state features
        state = [
            self.iteration / 100.0,  # Normalized iteration
            len(self.y_data) / (10 + 100),  # Normalized dataset size
            np.min(self.y_data),  # Best value
            self.y_data[-1],  # Current value
            self.improvement_history[-1] if self.improvement_history else 0.0,  # Recent improvement
            np.var(self.optimization_history[-5:]) if len(self.optimization_history) >= 5 else 1.0,  # Convergence
        ]
        
        # Add recent exploration/exploitation history (5 steps)
        recent_exploration = self.exploration_scores[-5:] if len(self.exploration_scores) >= 5 else [0.0] * 5
        recent_exploitation = self.exploitation_scores[-5:] if len(self.exploitation_scores) >= 5 else [0.0] * 5
        
        state.extend(recent_exploration)
        state.extend(recent_exploitation)
        
        return np.array(state, dtype=np.float32)
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of optimization results."""
        if len(self.y_data) == 0:
            return {}
        
        best_idx = np.argmin(self.y_data)
        
        return {
            "best_value": self.y_data[best_idx],
            "best_solution": self.x_data[best_idx],
            "total_evaluations": len(self.y_data),
            "total_improvements": sum(1 for imp in self.improvement_history if imp > 0),
            "optimization_history": self.optimization_history.copy(),
            "beta_history": self.beta_history.copy(),
            "improvement_history": self.improvement_history.copy(),
            "exploration_scores": self.exploration_scores.copy(),
            "exploitation_scores": self.exploitation_scores.copy()
        }
