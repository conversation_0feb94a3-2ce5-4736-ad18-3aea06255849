# RL-QUBO Training Configuration

# Random seed for reproducibility
seed: 42

# Device configuration
device: "auto"  # "auto", "cpu", "cuda"

# Logging configuration
verbose: 1
log_level: "INFO"

# Training environment configuration
environment:
  problem_dim: 100
  max_iterations: 50
  initial_dataset_size: 10
  beta_range: [0.0, 2.0]
  reward_scale: 1.0
  convergence_threshold: 1e-6
  history_length: 5

# Evaluation environment configuration
evaluation:
  problem_dim: 100
  max_iterations: 50
  initial_dataset_size: 10
  beta_range: [0.0, 2.0]
  reward_scale: 1.0
  convergence_threshold: 1e-6
  history_length: 5

# RL training configuration
training:
  learning_rate: 3e-4
  n_steps: 2048
  batch_size: 64
  n_epochs: 10
  gamma: 0.99
  gae_lambda: 0.95
  clip_range: 0.2
  ent_coef: 0.01
  vf_coef: 0.5
  max_grad_norm: 0.5
  target_kl: null

# Training parameters
total_timesteps: 100000
eval_freq: 10000
n_eval_episodes: 5

# Benchmark configuration
benchmark_iterations: 30
benchmark_runs: 3

# FMQA configuration
fmqa:
  k: 10  # FM hyperparameter
  epochs: 2000
  learning_rate: 0.1
  batch_size: 8
  train_split: 0.8
  timeout_ms: 2000

# Wandb configuration
wandb:
  project: "rl-qubo"
  entity: null  # Set your wandb entity if needed
  tags: ["rl", "qubo", "fmqa", "optimization"]
  notes: "RL-enhanced FMQA training experiment"
