{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RL-QUBO: Reinforcement Learning for QUBO Model Construction\n", "\n", "This notebook demonstrates the RL-QUBO approach for enhancing FMQA with reinforcement learning.\n", "\n", "## Overview\n", "\n", "The RL-QUBO method combines:\n", "1. **Factorization Machine (FM)** as a surrogate model\n", "2. **Exploration term** based on Hamming distances\n", "3. **Reinforcement Learning** to learn optimal β parameter selection\n", "4. **Quantum Annealing** for QUBO optimization\n", "\n", "### Key Innovation\n", "\n", "Instead of using a fixed exploration-exploitation trade-off, we use an RL agent to dynamically learn the optimal β parameter:\n", "\n", "```\n", "α(x) = f̂_mean(x) - β_t * h(x)\n", "```\n", "\n", "Where β_t is learned by the RL agent based on the current optimization state."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "from pathlib import Path\n", "import sys\n", "\n", "# Add src to path\n", "sys.path.append('../')\n", "\n", "from src.rl_qubo_env import RLQUBOEnvironment\n", "from src.rl_agent import RLAgent, TrainingConfig\n", "from src.rl_fmqa import RLEnhancedFMQA\n", "from src.evaluation import BenchmarkProblems, EvaluationFramework\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"RL-QUBO Framework Loaded Successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Problem Setup\n", "\n", "Let's start by creating a benchmark optimization problem to demonstrate the RL-QUBO approach."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a quadratic optimization problem\n", "problem_dim = 50\n", "problem_func = BenchmarkProblems.quadratic_problem(d=problem_dim, seed=42)\n", "\n", "# Test the problem function\n", "test_x = np.random.choice([0, 1], size=problem_dim)\n", "test_value = problem_func(test_x)\n", "\n", "print(f\"Problem dimension: {problem_dim}\")\n", "print(f\"Test evaluation: {test_value:.4f}\")\n", "print(f\"Problem type: Quadratic QUBO\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. RL Environment Setup\n", "\n", "The RL environment defines the state space, action space, and reward function for learning optimal β parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create RL environment\n", "env = RLQUBOEnvironment(\n", "    problem_dim=problem_dim,\n", "    max_iterations=30,\n", "    initial_dataset_size=5,\n", "    beta_range=(0.0, 2.0),\n", "    reward_scale=1.0,\n", "    history_length=5\n", ")\n", "\n", "print(f\"Action space: {env.action_space}\")\n", "print(f\"Observation space: {env.observation_space}\")\n", "\n", "# Test environment\n", "state, info = env.reset()\n", "print(f\"\\nInitial state shape: {state.shape}\")\n", "print(f\"Initial state: {state[:6]}...\")  # Show first 6 elements"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. RL Agent Training\n", "\n", "We'll train a PPO agent to learn optimal β parameter selection strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create training configuration\n", "training_config = TrainingConfig(\n", "    learning_rate=3e-4,\n", "    n_steps=1024,\n", "    batch_size=32,\n", "    n_epochs=5,\n", "    gamma=0.99,\n", "    gae_lambda=0.95\n", ")\n", "\n", "# Create RL agent\n", "agent = RLAgent(\n", "    env=env,\n", "    config=training_config,\n", "    verbose=1\n", ")\n", "\n", "print(\"RL Agent created successfully!\")\n", "print(f\"Training configuration: {training_config}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train the agent (this may take a few minutes)\n", "print(\"Training RL agent...\")\n", "agent.train(total_timesteps=10000, log_interval=1)\n", "print(\"Training completed!\")\n", "\n", "# Save the trained agent\n", "Path(\"../models\").mkdir(exist_ok=True)\n", "agent.save(\"../models/demo_rl_agent\")\n", "print(\"Agent saved to ../models/demo_rl_agent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. RL-Enhanced FMQA Demonstration\n", "\n", "Now let's compare different optimization strategies using the RL-enhanced FMQA framework."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_optimization_strategy(strategy_name, beta_strategy, n_iterations=20, n_initial=5):\n", "    \"\"\"Run optimization with a specific β strategy.\"\"\"\n", "    \n", "    # Create RL-FMQA instance\n", "    rl_fmqa = RLEnhancedFMQA(\n", "        problem_dim=problem_dim,\n", "        blackbox_func=problem_func\n", "    )\n", "    \n", "    # Initialize dataset\n", "    rl_fmqa.initialize_dataset(n_initial)\n", "    \n", "    print(f\"\\nRunning {strategy_name} strategy...\")\n", "    \n", "    # Run optimization\n", "    for i in range(n_iterations):\n", "        if strategy_name == \"RL Agent\":\n", "            # Use trained RL agent\n", "            state = rl_fmqa.get_state_representation()\n", "            beta, _ = agent.predict(state, deterministic=True)\n", "            beta = beta[0]\n", "        elif strategy_name == \"Random β\":\n", "            # Random β selection\n", "            beta = np.random.uniform(0.0, 2.0)\n", "        else:\n", "            # Fixed β\n", "            beta = beta_strategy\n", "        \n", "        step_result = rl_fmqa.optimize_step(beta)\n", "        \n", "        if i % 5 == 0:\n", "            print(f\"  Iteration {i}: best = {step_result['best_value']:.4f}, β = {beta:.3f}\")\n", "    \n", "    return rl_fmqa.get_optimization_summary()\n", "\n", "# Test different strategies\n", "strategies = {\n", "    \"Fixed β=0.5\": 0.5,\n", "    \"Fixed β=1.0\": 1.0,\n", "    \"Random β\": \"random\",\n", "    \"RL Agent\": \"rl\"\n", "}\n", "\n", "results = {}\n", "for strategy_name, beta_strategy in strategies.items():\n", "    results[strategy_name] = run_optimization_strategy(strategy_name, beta_strategy)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Results Analysis and Visualization\n", "\n", "Let's analyze and visualize the performance of different strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive comparison plots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Convergence comparison\n", "ax1 = axes[0, 0]\n", "for strategy_name, result in results.items():\n", "    if 'optimization_history' in result:\n", "        ax1.plot(result['optimization_history'], label=strategy_name, marker='o', markersize=4)\n", "\n", "ax1.set_xlabel('Iteration')\n", "ax1.set_ylabel('Best Objective Value')\n", "ax1.set_title('Convergence Comparison')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 2. β parameter evolution\n", "ax2 = axes[0, 1]\n", "for strategy_name, result in results.items():\n", "    if 'beta_history' in result:\n", "        ax2.plot(result['beta_history'], label=strategy_name, marker='s', markersize=3)\n", "\n", "ax2.set_xlabel('Iteration')\n", "ax2.set_ylabel('β Parameter')\n", "ax2.set_title('β Parameter Evolution')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 3. Exploration vs Exploitation\n", "ax3 = axes[1, 0]\n", "for strategy_name, result in results.items():\n", "    if 'exploration_scores' in result and 'exploitation_scores' in result:\n", "        ax3.scatter(result['exploration_scores'], result['exploitation_scores'], \n", "                   label=strategy_name, alpha=0.6, s=30)\n", "\n", "ax3.set_xlabel('Exploration Score')\n", "ax3.set_ylabel('Exploitation Score')\n", "ax3.set_title('Exploration vs Exploitation Balance')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 4. Final performance comparison\n", "ax4 = axes[1, 1]\n", "strategy_names = list(results.keys())\n", "best_values = [results[name]['best_value'] for name in strategy_names]\n", "total_improvements = [results[name]['total_improvements'] for name in strategy_names]\n", "\n", "x_pos = np.arange(len(strategy_names))\n", "bars = ax4.bar(x_pos, best_values, alpha=0.7)\n", "ax4.set_xlabel('Strategy')\n", "ax4.set_ylabel('Best Objective Value')\n", "ax4.set_title('Final Performance Comparison')\n", "ax4.set_xticks(x_pos)\n", "ax4.set_xticklabels(strategy_names, rotation=45)\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, best_values):\n", "    ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "            f'{value:.3f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('../results/rl_qubo_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print detailed results summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DETAILED RESULTS SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "for strategy_name, result in results.items():\n", "    print(f\"\\n{strategy_name}:\")\n", "    print(f\"  Best Value: {result['best_value']:.6f}\")\n", "    print(f\"  Total Evaluations: {result['total_evaluations']}\")\n", "    print(f\"  Total Improvements: {result['total_improvements']}\")\n", "    \n", "    if 'beta_history' in result:\n", "        beta_mean = np.mean(result['beta_history'])\n", "        beta_std = np.std(result['beta_history'])\n", "        print(f\"  β Statistics: {beta_mean:.3f} ± {beta_std:.3f}\")\n", "    \n", "    if 'exploration_scores' in result:\n", "        exp_mean = np.mean(result['exploration_scores'])\n", "        print(f\"  Avg Exploration Score: {exp_mean:.3f}\")\n", "\n", "# Find best performing strategy\n", "best_strategy = min(results.keys(), key=lambda k: results[k]['best_value'])\n", "print(f\"\\n🏆 Best Strategy: {best_strategy}\")\n", "print(f\"   Best Value: {results[best_strategy]['best_value']:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. RL Agent Policy Analysis\n", "\n", "Let's analyze what the RL agent has learned about optimal β parameter selection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze RL agent's learned policy\n", "def analyze_rl_policy(agent, n_samples=100):\n", "    \"\"\"Analyze the RL agent's learned policy.\"\"\"\n", "    \n", "    # Generate diverse states\n", "    states = []\n", "    actions = []\n", "    \n", "    for i in range(n_samples):\n", "        # Create synthetic states with different characteristics\n", "        iteration_progress = i / n_samples\n", "        dataset_size = 10 + i * 0.5\n", "        best_value = 100 - i * 0.8  # Improving over time\n", "        improvement_rate = max(0, 1.0 - iteration_progress)\n", "        convergence_metric = iteration_progress * 0.1\n", "        \n", "        # Create state vector\n", "        state = np.array([\n", "            iteration_progress,\n", "            dataset_size / 60,  # Normalized\n", "            best_value,\n", "            best_value + np.random.normal(0, 1),\n", "            improvement_rate,\n", "            convergence_metric,\n", "            # Add dummy history\n", "            *[0.5] * 5,  # exploration history\n", "            *[0.5] * 5   # exploitation history\n", "        ], dtype=np.float32)\n", "        \n", "        # Get agent's action\n", "        action, _ = agent.predict(state, deterministic=True)\n", "        \n", "        states.append(state)\n", "        actions.append(action[0])\n", "    \n", "    return states, actions\n", "\n", "# Analyze the policy\n", "states, actions = analyze_rl_policy(agent)\n", "\n", "# Plot policy analysis\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "# 1. β vs iteration progress\n", "iteration_progress = [s[0] for s in states]\n", "axes[0].scatter(iteration_progress, actions, alpha=0.6)\n", "axes[0].set_xlabel('Iteration Progress')\n", "axes[0].set_ylabel('β Parameter')\n", "axes[0].set_title('β vs Iteration Progress')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 2. β vs improvement rate\n", "improvement_rates = [s[4] for s in states]\n", "axes[1].scatter(improvement_rates, actions, alpha=0.6, color='orange')\n", "axes[1].set_xlabel('Improvement Rate')\n", "axes[1].set_ylabel('β Parameter')\n", "axes[1].set_title('β vs Improvement Rate')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# 3. β distribution\n", "axes[2].hist(actions, bins=20, alpha=0.7, color='green')\n", "axes[2].set_xlabel('β Parameter')\n", "axes[2].set_ylabel('Frequency')\n", "axes[2].set_title('β Parameter Distribution')\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('../results/rl_policy_analysis.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(f\"\\nRL Policy Analysis:\")\n", "print(f\"β range: [{min(actions):.3f}, {max(actions):.3f}]\")\n", "print(f\"β mean: {np.mean(actions):.3f} ± {np.std(actions):.3f}\")\n", "print(f\"Correlation with iteration progress: {np.corrcoef(iteration_progress, actions)[0,1]:.3f}\")\n", "print(f\"Correlation with improvement rate: {np.corrcoef(improvement_rates, actions)[0,1]:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Comprehensive Benchmark\n", "\n", "Finally, let's run a comprehensive benchmark comparing RL-QUBO against traditional methods on multiple problem types."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create evaluation framework\n", "evaluator = EvaluationFramework(output_dir=\"../results\")\n", "\n", "# Define benchmark problems\n", "problems = {\n", "    'quadratic': lambda d, seed: BenchmarkProblems.quadratic_problem(d, seed),\n", "    'ising': lambda d, seed: BenchmarkProblems.ising_problem(d, seed),\n", "}\n", "\n", "# Define optimization methods\n", "def rl_method(problem_func, problem_dim, n_iterations, n_initial, **kwargs):\n", "    \"\"\"RL-enhanced FMQA method.\"\"\"\n", "    rl_fmqa = RLEnhancedFMQA(problem_dim, problem_func)\n", "    rl_fmqa.initialize_dataset(n_initial)\n", "    \n", "    for _ in range(n_iterations):\n", "        state = rl_fmqa.get_state_representation()\n", "        beta, _ = agent.predict(state, deterministic=True)\n", "        rl_fmqa.optimize_step(beta[0])\n", "    \n", "    return rl_fmqa.get_optimization_summary()\n", "\n", "def fixed_beta_method(problem_func, problem_dim, n_iterations, n_initial, beta=0.5, **kwargs):\n", "    \"\"\"Traditional FMQA with fixed β.\"\"\"\n", "    rl_fmqa = RLEnhancedFMQA(problem_dim, problem_func)\n", "    rl_fmqa.initialize_dataset(n_initial)\n", "    \n", "    for _ in range(n_iterations):\n", "        rl_fmqa.optimize_step(beta)\n", "    \n", "    return rl_fmqa.get_optimization_summary()\n", "\n", "def random_beta_method(problem_func, problem_dim, n_iterations, n_initial, **kwargs):\n", "    \"\"\"Random β selection baseline.\"\"\"\n", "    rl_fmqa = RLEnhancedFMQA(problem_dim, problem_func)\n", "    rl_fmqa.initialize_dataset(n_initial)\n", "    \n", "    rng = np.random.default_rng(42)\n", "    for _ in range(n_iterations):\n", "        beta = rng.uniform(0.0, 2.0)\n", "        rl_fmqa.optimize_step(beta)\n", "    \n", "    return rl_fmqa.get_optimization_summary()\n", "\n", "methods = {\n", "    'RL-QUBO': rl_method,\n", "    'Fixed-β-0.5': lambda **kwargs: fixed_beta_method(beta=0.5, **kwargs),\n", "    'Fixed-β-1.0': lambda **kwargs: fixed_beta_method(beta=1.0, **kwargs),\n", "    'Random-β': random_beta_method\n", "}\n", "\n", "print(\"Running comprehensive benchmark...\")\n", "print(\"This may take several minutes...\")\n", "\n", "# Run comparison\n", "results_df = evaluator.compare_methods(\n", "    methods=methods,\n", "    problems=problems,\n", "    problem_dims=[30, 50],\n", "    n_iterations=15,\n", "    n_runs=3,\n", "    n_initial=5\n", ")\n", "\n", "print(\"Benchmark completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display benchmark results\n", "print(\"\\nBenchmark Results Summary:\")\n", "print(\"=\"*50)\n", "\n", "# Group by method and calculate statistics\n", "summary = results_df.groupby('method').agg({\n", "    'best_value': ['mean', 'std', 'min'],\n", "    'convergence_iteration': ['mean', 'std'],\n", "    'runtime': ['mean', 'std']\n", "}).round(4)\n", "\n", "print(summary)\n", "\n", "# Generate comparison plots\n", "evaluator.plot_performance_comparison(\n", "    results_df, \n", "    save_path=\"../results/benchmark_performance.png\"\n", ")\n", "\n", "evaluator.plot_convergence_comparison(\n", "    results_df,\n", "    save_path=\"../results/benchmark_convergence.png\"\n", ")\n", "\n", "# Save results\n", "evaluator.save_results(results_df, \"benchmark_results.csv\")\n", "print(\"\\nResults saved to ../results/benchmark_results.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusions\n", "\n", "### Key Findings\n", "\n", "1. **Adaptive Strategy**: The RL agent learns to adapt β parameters based on optimization progress\n", "2. **Improved Performance**: RL-QUBO typically outperforms fixed β strategies\n", "3. **Exploration-Exploitation Balance**: The agent learns when to explore vs exploit\n", "4. **Problem Generalization**: The approach works across different problem types\n", "\n", "### Future Directions\n", "\n", "1. **Multi-objective Optimization**: Extend to multi-objective QUBO problems\n", "2. **Transfer Learning**: Pre-train agents on diverse problems for faster adaptation\n", "3. **Quantum Hardware**: Integration with actual quantum annealing devices\n", "4. **Advanced RL**: Explore other RL algorithms (SAC, TD3, etc.)\n", "\n", "### Practical Applications\n", "\n", "- **Materials Discovery**: Optimize material properties\n", "- **Financial Portfolio**: Portfolio optimization problems\n", "- **Logistics**: Vehicle routing and scheduling\n", "- **Machine Learning**: Feature selection and hyperparameter optimization"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}