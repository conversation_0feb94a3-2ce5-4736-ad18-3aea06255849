"""
Setup script for RL-QUBO: Reinforcement Learning for QUBO Model Construction
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()

# Read requirements
requirements = []
with open("requirements.txt", "r") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="rl-qubo",
    version="0.1.0",
    author="RL-QUBO Development Team",
    author_email="<EMAIL>",
    description="Reinforcement Learning for QUBO Model Construction",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/rl-qubo",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Mathematics",
        "Topic :: Scientific/Engineering :: Physics",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=3.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
            "myst-parser>=0.17.0",
        ],
        "quantum": [
            "dwave-ocean-sdk>=6.0.0",
            "amplify-sdk>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "rl-qubo-train=train_rl_agent:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.json"],
    },
    project_urls={
        "Bug Reports": "https://github.com/your-org/rl-qubo/issues",
        "Source": "https://github.com/your-org/rl-qubo",
        "Documentation": "https://rl-qubo.readthedocs.io/",
    },
)
