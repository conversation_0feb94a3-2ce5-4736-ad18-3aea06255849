"""
Unit tests for RL-QUBO Environment
"""

import pytest
import numpy as np
import gymnasium as gym
from src.rl_qubo_env import RLQUBOEnvironment, OptimizationState


class TestRLQUBOEnvironment:
    """Test cases for RLQUBOEnvironment."""
    
    def setup_method(self):
        """Setup test environment."""
        self.env = RLQUBOEnvironment(
            problem_dim=10,
            max_iterations=20,
            initial_dataset_size=5,
            beta_range=(0.0, 2.0),
            reward_scale=1.0,
            convergence_threshold=1e-6,
            history_length=3
        )
    
    def test_initialization(self):
        """Test environment initialization."""
        assert self.env.problem_dim == 10
        assert self.env.max_iterations == 20
        assert self.env.initial_dataset_size == 5
        assert self.env.beta_range == (0.0, 2.0)
        assert self.env.reward_scale == 1.0
        assert self.env.convergence_threshold == 1e-6
        assert self.env.history_length == 3
    
    def test_action_space(self):
        """Test action space configuration."""
        assert isinstance(self.env.action_space, gym.spaces.Box)
        assert self.env.action_space.shape == (1,)
        assert self.env.action_space.low[0] == 0.0
        assert self.env.action_space.high[0] == 2.0
    
    def test_observation_space(self):
        """Test observation space configuration."""
        assert isinstance(self.env.observation_space, gym.spaces.Box)
        expected_dim = 6 + 2 * self.env.history_length  # 6 + 2*3 = 12
        assert self.env.observation_space.shape == (expected_dim,)
    
    def test_reset(self):
        """Test environment reset."""
        state, info = self.env.reset()
        
        # Check state shape and type
        assert isinstance(state, np.ndarray)
        assert state.shape == self.env.observation_space.shape
        assert state.dtype == np.float32
        
        # Check initial values
        assert self.env.current_iteration == 0
        assert self.env.dataset_size == self.env.initial_dataset_size
        assert self.env.best_value == np.inf
        assert len(self.env.exploration_history) == self.env.history_length
        assert len(self.env.exploitation_history) == self.env.history_length
        
        # Check info dictionary
        assert isinstance(info, dict)
        assert 'iteration' in info
        assert 'dataset_size' in info
        assert 'best_value' in info
    
    def test_step(self):
        """Test environment step function."""
        self.env.reset()
        
        # Take a step
        action = np.array([1.0])
        state, reward, terminated, truncated, info = self.env.step(action)
        
        # Check return types
        assert isinstance(state, np.ndarray)
        assert isinstance(reward, float)
        assert isinstance(terminated, bool)
        assert isinstance(truncated, bool)
        assert isinstance(info, dict)
        
        # Check state update
        assert self.env.current_iteration == 1
        assert self.env.dataset_size == self.env.initial_dataset_size + 1
        
        # Check state shape
        assert state.shape == self.env.observation_space.shape
    
    def test_action_clipping(self):
        """Test that actions are properly clipped to valid range."""
        self.env.reset()
        
        # Test action outside range
        action_high = np.array([5.0])  # Above max
        action_low = np.array([-1.0])  # Below min
        
        # These should not raise errors and should be clipped
        state1, _, _, _, _ = self.env.step(action_high)
        self.env.reset()
        state2, _, _, _, _ = self.env.step(action_low)
        
        # Check that environment handled the clipping gracefully
        assert isinstance(state1, np.ndarray)
        assert isinstance(state2, np.ndarray)
    
    def test_termination_conditions(self):
        """Test environment termination conditions."""
        self.env.reset()
        
        # Run until max iterations
        terminated = False
        iteration_count = 0
        
        while not terminated and iteration_count < self.env.max_iterations + 5:
            action = np.array([1.0])
            _, _, terminated, _, _ = self.env.step(action)
            iteration_count += 1
        
        # Should terminate at max iterations
        assert terminated
        assert self.env.current_iteration >= self.env.max_iterations
    
    def test_reward_calculation(self):
        """Test reward calculation."""
        self.env.reset()
        
        # Take several steps and check rewards
        rewards = []
        for i in range(5):
            action = np.array([0.5 + 0.1 * i])  # Varying beta
            _, reward, _, _, _ = self.env.step(action)
            rewards.append(reward)
        
        # Rewards should be finite numbers
        assert all(np.isfinite(r) for r in rewards)
        assert all(isinstance(r, float) for r in rewards)
    
    def test_state_representation(self):
        """Test state representation consistency."""
        state1, _ = self.env.reset()
        
        # Take a step
        action = np.array([1.0])
        state2, _, _, _, _ = self.env.step(action)
        
        # States should have same shape but different values
        assert state1.shape == state2.shape
        assert not np.array_equal(state1, state2)
        
        # Check that state values are reasonable
        assert np.all(np.isfinite(state1))
        assert np.all(np.isfinite(state2))
    
    def test_info_dictionary(self):
        """Test info dictionary contents."""
        self.env.reset()
        
        action = np.array([1.0])
        _, _, _, _, info = self.env.step(action)
        
        # Check required keys
        required_keys = [
            'iteration', 'dataset_size', 'best_value', 
            'improvement_rate', 'convergence_metric',
            'episode_reward', 'episode_improvements',
            'beta_history', 'value_history'
        ]
        
        for key in required_keys:
            assert key in info, f"Missing key: {key}"
    
    def test_multiple_episodes(self):
        """Test multiple episode runs."""
        for episode in range(3):
            state, _ = self.env.reset()
            
            # Run a short episode
            for step in range(5):
                action = np.array([np.random.uniform(0.0, 2.0)])
                state, reward, terminated, truncated, info = self.env.step(action)
                
                if terminated or truncated:
                    break
            
            # Environment should be properly reset for next episode
            assert isinstance(state, np.ndarray)
            assert state.shape == self.env.observation_space.shape
    
    def test_render(self):
        """Test render function."""
        self.env.reset()
        
        # Render should not raise errors
        try:
            self.env.render(mode="human")
            render_result = self.env.render(mode="rgb_array")
            # For this implementation, render returns None
            assert render_result is None
        except Exception as e:
            pytest.fail(f"Render function raised an exception: {e}")
    
    def test_simulation_consistency(self):
        """Test that simulation produces consistent results."""
        # Reset with same seed should produce similar initial conditions
        state1, _ = self.env.reset(seed=42)
        state2, _ = self.env.reset(seed=42)
        
        # Initial states should be identical
        np.testing.assert_array_equal(state1, state2)
    
    def test_exploration_exploitation_tracking(self):
        """Test exploration and exploitation score tracking."""
        self.env.reset()
        
        # Take steps with different beta values
        betas = [0.1, 0.5, 1.0, 1.5, 1.9]
        
        for beta in betas:
            action = np.array([beta])
            _, _, _, _, info = self.env.step(action)
            
            # Check that beta is recorded
            assert len(info['beta_history']) > 0
            assert info['beta_history'][-1] == beta
        
        # Check history lengths
        assert len(self.env.exploration_history) == self.env.history_length
        assert len(self.env.exploitation_history) == self.env.history_length


class TestOptimizationState:
    """Test cases for OptimizationState dataclass."""
    
    def test_optimization_state_creation(self):
        """Test OptimizationState creation."""
        state = OptimizationState(
            iteration=5,
            dataset_size=15,
            best_value=10.5,
            current_value=12.0,
            improvement_rate=0.1,
            exploration_history=[0.1, 0.2, 0.3],
            exploitation_history=[0.9, 0.8, 0.7],
            convergence_metric=0.05
        )
        
        assert state.iteration == 5
        assert state.dataset_size == 15
        assert state.best_value == 10.5
        assert state.current_value == 12.0
        assert state.improvement_rate == 0.1
        assert state.exploration_history == [0.1, 0.2, 0.3]
        assert state.exploitation_history == [0.9, 0.8, 0.7]
        assert state.convergence_metric == 0.05


if __name__ == "__main__":
    pytest.main([__file__])
