"""
Training Script for RL-QUBO Agent

This script trains a reinforcement learning agent to learn optimal β parameter
selection strategies for the RL-QUBO algorithm.
"""

import argparse
import logging
import numpy as np
import torch
from pathlib import Path
import yaml
from datetime import datetime
import wandb
from typing import Dict, Any

from src.rl_qubo_env import RLQUBOEnvironment
from src.rl_agent import RLAgent, TrainingConfig
from src.evaluation import BenchmarkProblems
from src.rl_fmqa import R<PERSON>nhancedFMQA


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('training.log'),
            logging.StreamHandler()
        ]
    )


def load_config(config_path: str) -> Dict[str, Any]:
    """Load training configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def create_training_environment(config: Dict[str, Any]) -> RLQUBOEnvironment:
    """Create training environment based on configuration."""
    env_config = config.get('environment', {})
    
    env = RLQUBOEnvironment(
        problem_dim=env_config.get('problem_dim', 100),
        max_iterations=env_config.get('max_iterations', 50),
        initial_dataset_size=env_config.get('initial_dataset_size', 10),
        beta_range=tuple(env_config.get('beta_range', [0.0, 2.0])),
        reward_scale=env_config.get('reward_scale', 1.0),
        convergence_threshold=env_config.get('convergence_threshold', 1e-6),
        history_length=env_config.get('history_length', 5)
    )
    
    return env


def create_evaluation_environment(config: Dict[str, Any]) -> RLQUBOEnvironment:
    """Create evaluation environment with different parameters."""
    eval_config = config.get('evaluation', {})
    
    # Use different problem instances for evaluation
    env = RLQUBOEnvironment(
        problem_dim=eval_config.get('problem_dim', 100),
        max_iterations=eval_config.get('max_iterations', 50),
        initial_dataset_size=eval_config.get('initial_dataset_size', 10),
        beta_range=tuple(eval_config.get('beta_range', [0.0, 2.0])),
        reward_scale=eval_config.get('reward_scale', 1.0),
        convergence_threshold=eval_config.get('convergence_threshold', 1e-6),
        history_length=eval_config.get('history_length', 5)
    )
    
    return env


def train_agent(
    config: Dict[str, Any],
    use_wandb: bool = False,
    wandb_project: str = "rl-qubo"
) -> RLAgent:
    """Train the RL agent."""
    
    logger = logging.getLogger(__name__)
    logger.info("Starting RL agent training")
    
    # Initialize wandb if requested
    if use_wandb:
        wandb.init(
            project=wandb_project,
            config=config,
            name=f"rl-qubo-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        )
    
    # Create environments
    train_env = create_training_environment(config)
    eval_env = create_evaluation_environment(config)
    
    # Create training configuration
    training_config = TrainingConfig(
        **config.get('training', {})
    )
    
    # Create agent
    agent = RLAgent(
        env=train_env,
        config=training_config,
        device=config.get('device', 'auto'),
        verbose=config.get('verbose', 1),
        tensorboard_log="./tensorboard_logs/" if not use_wandb else None
    )
    
    # Training parameters
    total_timesteps = config.get('total_timesteps', 100000)
    eval_freq = config.get('eval_freq', 10000)
    n_eval_episodes = config.get('n_eval_episodes', 5)
    
    # Train the agent
    agent.train(
        total_timesteps=total_timesteps,
        eval_env=eval_env,
        eval_freq=eval_freq,
        n_eval_episodes=n_eval_episodes
    )
    
    # Save the trained agent
    model_dir = Path("models")
    model_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_path = model_dir / f"rl_qubo_agent_{timestamp}"
    agent.save(str(model_path))
    
    logger.info(f"Training completed. Model saved to {model_path}")
    
    # Final evaluation
    final_eval = agent.evaluate(eval_env, n_episodes=20, deterministic=True)
    logger.info(f"Final evaluation: {final_eval}")
    
    if use_wandb:
        wandb.log(final_eval)
        wandb.finish()
    
    return agent


def evaluate_trained_agent(
    agent_path: str,
    config: Dict[str, Any],
    n_episodes: int = 10
) -> Dict[str, Any]:
    """Evaluate a trained agent."""
    
    logger = logging.getLogger(__name__)
    logger.info(f"Evaluating trained agent from {agent_path}")
    
    # Create evaluation environment
    eval_env = create_evaluation_environment(config)
    
    # Load agent
    agent = RLAgent(env=eval_env)
    agent.load(agent_path)
    
    # Evaluate
    results = agent.evaluate(
        eval_env,
        n_episodes=n_episodes,
        deterministic=True,
        render=False
    )
    
    logger.info(f"Evaluation results: {results}")
    return results


def benchmark_against_baselines(
    agent_path: str,
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """Benchmark trained agent against baseline methods."""
    
    logger = logging.getLogger(__name__)
    logger.info("Running benchmark comparison")
    
    from src.evaluation import EvaluationFramework, BenchmarkProblems
    
    # Create evaluation framework
    evaluator = EvaluationFramework(output_dir="benchmark_results")
    
    # Define benchmark problems
    problems = {
        'quadratic': lambda d, seed: BenchmarkProblems.quadratic_problem(d, seed),
        'ising': lambda d, seed: BenchmarkProblems.ising_problem(d, seed),
        'max_cut': lambda d, seed: BenchmarkProblems.max_cut_problem(d, seed=seed)
    }
    
    # Define methods to compare
    def rl_fmqa_method(problem_func, problem_dim, n_iterations, n_initial, **kwargs):
        """RL-enhanced FMQA method."""
        # Load trained agent
        eval_env = create_evaluation_environment(config)
        agent = RLAgent(env=eval_env)
        agent.load(agent_path)
        
        # Create RL-FMQA instance
        rl_fmqa = RLEnhancedFMQA(
            problem_dim=problem_dim,
            blackbox_func=problem_func
        )
        
        # Initialize dataset
        rl_fmqa.initialize_dataset(n_initial)
        
        # Run optimization with RL agent
        for _ in range(n_iterations):
            state = rl_fmqa.get_state_representation()
            beta, _ = agent.predict(state, deterministic=True)
            rl_fmqa.optimize_step(beta[0])
        
        return rl_fmqa.get_optimization_summary()
    
    def traditional_fmqa_method(problem_func, problem_dim, n_iterations, n_initial, **kwargs):
        """Traditional FMQA with fixed β."""
        rl_fmqa = RLEnhancedFMQA(
            problem_dim=problem_dim,
            blackbox_func=problem_func
        )
        
        rl_fmqa.initialize_dataset(n_initial)
        
        # Use fixed β = 0.5
        for _ in range(n_iterations):
            rl_fmqa.optimize_step(0.5)
        
        return rl_fmqa.get_optimization_summary()
    
    def random_beta_method(problem_func, problem_dim, n_iterations, n_initial, **kwargs):
        """Random β selection baseline."""
        rl_fmqa = RLEnhancedFMQA(
            problem_dim=problem_dim,
            blackbox_func=problem_func
        )
        
        rl_fmqa.initialize_dataset(n_initial)
        
        # Use random β values
        rng = np.random.default_rng(42)
        for _ in range(n_iterations):
            beta = rng.uniform(0.0, 2.0)
            rl_fmqa.optimize_step(beta)
        
        return rl_fmqa.get_optimization_summary()
    
    methods = {
        'RL-FMQA': rl_fmqa_method,
        'Traditional-FMQA': traditional_fmqa_method,
        'Random-Beta': random_beta_method
    }
    
    # Run comparison
    results_df = evaluator.compare_methods(
        methods=methods,
        problems=problems,
        problem_dims=[50, 100],
        n_iterations=config.get('benchmark_iterations', 30),
        n_runs=config.get('benchmark_runs', 3),
        n_initial=10
    )
    
    # Generate plots and report
    evaluator.plot_convergence_comparison(
        results_df,
        save_path="benchmark_results/convergence_comparison.png"
    )
    
    evaluator.plot_performance_comparison(
        results_df,
        save_path="benchmark_results/performance_comparison.png"
    )
    
    report = evaluator.generate_report(results_df)
    evaluator.save_results(results_df)
    
    logger.info("Benchmark comparison completed")
    return report


def main():
    """Main training script."""
    parser = argparse.ArgumentParser(description="Train RL-QUBO Agent")
    parser.add_argument(
        "--config",
        type=str,
        default="config/training_config.yaml",
        help="Path to training configuration file"
    )
    parser.add_argument(
        "--mode",
        type=str,
        choices=["train", "evaluate", "benchmark"],
        default="train",
        help="Mode: train new agent, evaluate existing agent, or run benchmark"
    )
    parser.add_argument(
        "--agent-path",
        type=str,
        help="Path to trained agent (for evaluate/benchmark modes)"
    )
    parser.add_argument(
        "--use-wandb",
        action="store_true",
        help="Use Weights & Biases for logging"
    )
    parser.add_argument(
        "--wandb-project",
        type=str,
        default="rl-qubo",
        help="Wandb project name"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Load configuration
    try:
        config = load_config(args.config)
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {args.config}")
        return
    
    # Set random seeds for reproducibility
    seed = config.get('seed', 42)
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    if args.mode == "train":
        agent = train_agent(config, args.use_wandb, args.wandb_project)
        
    elif args.mode == "evaluate":
        if not args.agent_path:
            logger.error("Agent path required for evaluation mode")
            return
        
        results = evaluate_trained_agent(args.agent_path, config)
        print(f"Evaluation results: {results}")
        
    elif args.mode == "benchmark":
        if not args.agent_path:
            logger.error("Agent path required for benchmark mode")
            return
        
        report = benchmark_against_baselines(args.agent_path, config)
        print("Benchmark report generated")


if __name__ == "__main__":
    main()
