"""
Basic RL-QUBO Example

This example demonstrates how to use the RL-QUBO framework to optimize
a simple quadratic function using reinforcement learning to learn
optimal exploration-exploitation trade-offs.
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.rl_qubo_env import RLQUBOEnvironment
from src.rl_agent import RLAgent, TrainingConfig
from src.rl_fmqa import RLEnhancedFMQA
from src.evaluation import BenchmarkProblems, EvaluationFramework


def create_simple_problem(d: int = 50) -> callable:
    """Create a simple quadratic optimization problem."""
    return BenchmarkProblems.quadratic_problem(d, seed=42)


def train_simple_agent():
    """Train an RL agent on a simple problem."""
    print("Training RL agent for QUBO parameter optimization...")
    
    # Create environment
    env = RLQUBOEnvironment(
        problem_dim=50,
        max_iterations=30,
        initial_dataset_size=5,
        beta_range=(0.0, 2.0),
        reward_scale=1.0
    )
    
    # Create agent with simple configuration
    config = TrainingConfig(
        learning_rate=3e-4,
        n_steps=1024,
        batch_size=32,
        n_epochs=5,
        gamma=0.99
    )
    
    agent = RLAgent(env=env, config=config, verbose=1)
    
    # Train for a short time
    agent.train(total_timesteps=10000, log_interval=1)
    
    # Save the agent
    agent.save("models/simple_rl_agent")
    print("Agent training completed and saved!")
    
    return agent


def demonstrate_rl_fmqa():
    """Demonstrate RL-enhanced FMQA optimization."""
    print("\nDemonstrating RL-enhanced FMQA...")
    
    # Create problem
    problem_func = create_simple_problem(50)
    
    # Create RL-FMQA instance
    rl_fmqa = RLEnhancedFMQA(
        problem_dim=50,
        blackbox_func=problem_func
    )
    
    # Initialize dataset
    x_init, y_init = rl_fmqa.initialize_dataset(5)
    print(f"Initial dataset: {len(x_init)} points, best value: {np.min(y_init):.4f}")
    
    # Run optimization with different β strategies
    strategies = {
        "Fixed β=0.5": 0.5,
        "Fixed β=1.0": 1.0,
        "Random β": "random"
    }
    
    results = {}
    
    for strategy_name, beta_strategy in strategies.items():
        print(f"\nTesting strategy: {strategy_name}")
        
        # Reset FMQA
        rl_fmqa_copy = RLEnhancedFMQA(50, problem_func)
        rl_fmqa_copy.x_data = x_init.copy()
        rl_fmqa_copy.y_data = y_init.copy()
        
        # Run optimization
        for i in range(20):
            if beta_strategy == "random":
                beta = np.random.uniform(0.0, 2.0)
            else:
                beta = beta_strategy
            
            step_result = rl_fmqa_copy.optimize_step(beta)
            
            if i % 5 == 0:
                print(f"  Iteration {i}: best = {step_result['best_value']:.4f}, β = {beta:.2f}")
        
        results[strategy_name] = rl_fmqa_copy.get_optimization_summary()
    
    return results


def compare_with_trained_agent():
    """Compare strategies including trained RL agent."""
    print("\nComparing with trained RL agent...")
    
    try:
        # Load trained agent
        env = RLQUBOEnvironment(problem_dim=50, max_iterations=30)
        agent = RLAgent(env=env)
        agent.load("models/simple_rl_agent")
        print("Loaded trained RL agent")
        
        # Create problem
        problem_func = create_simple_problem(50)
        
        # Test RL agent strategy
        rl_fmqa = RLEnhancedFMQA(50, problem_func)
        rl_fmqa.initialize_dataset(5)
        
        print("Running optimization with RL agent...")
        for i in range(20):
            state = rl_fmqa.get_state_representation()
            beta, _ = agent.predict(state, deterministic=True)
            step_result = rl_fmqa.optimize_step(beta[0])
            
            if i % 5 == 0:
                print(f"  Iteration {i}: best = {step_result['best_value']:.4f}, β = {beta[0]:.2f}")
        
        rl_result = rl_fmqa.get_optimization_summary()
        print(f"RL agent final result: {rl_result['best_value']:.4f}")
        
        return rl_result
        
    except Exception as e:
        print(f"Could not load trained agent: {e}")
        print("Please run train_simple_agent() first")
        return None


def plot_optimization_comparison(results: dict):
    """Plot comparison of different optimization strategies."""
    plt.figure(figsize=(12, 8))
    
    # Plot convergence histories
    plt.subplot(2, 2, 1)
    for strategy_name, result in results.items():
        if 'optimization_history' in result:
            plt.plot(result['optimization_history'], label=strategy_name, marker='o', markersize=3)
    
    plt.xlabel('Iteration')
    plt.ylabel('Best Objective Value')
    plt.title('Convergence Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot β parameter evolution
    plt.subplot(2, 2, 2)
    for strategy_name, result in results.items():
        if 'beta_history' in result:
            plt.plot(result['beta_history'], label=strategy_name, marker='s', markersize=3)
    
    plt.xlabel('Iteration')
    plt.ylabel('β Parameter')
    plt.title('β Parameter Evolution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot exploration vs exploitation scores
    plt.subplot(2, 2, 3)
    for strategy_name, result in results.items():
        if 'exploration_scores' in result and 'exploitation_scores' in result:
            plt.scatter(result['exploration_scores'], result['exploitation_scores'], 
                       label=strategy_name, alpha=0.6, s=20)
    
    plt.xlabel('Exploration Score')
    plt.ylabel('Exploitation Score')
    plt.title('Exploration vs Exploitation')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot final performance comparison
    plt.subplot(2, 2, 4)
    strategy_names = list(results.keys())
    best_values = [results[name]['best_value'] for name in strategy_names]
    
    bars = plt.bar(strategy_names, best_values, alpha=0.7)
    plt.ylabel('Best Objective Value')
    plt.title('Final Performance Comparison')
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for bar, value in zip(bars, best_values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('rl_qubo_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def run_comprehensive_benchmark():
    """Run a comprehensive benchmark comparison."""
    print("\nRunning comprehensive benchmark...")
    
    evaluator = EvaluationFramework(output_dir="example_results")
    
    # Define problems
    problems = {
        'quadratic_50': lambda d, seed: BenchmarkProblems.quadratic_problem(50, seed),
        'ising_50': lambda d, seed: BenchmarkProblems.ising_problem(50, seed)
    }
    
    # Define methods
    def fixed_beta_method(problem_func, problem_dim, n_iterations, n_initial, beta=0.5):
        rl_fmqa = RLEnhancedFMQA(problem_dim, problem_func)
        rl_fmqa.initialize_dataset(n_initial)
        
        for _ in range(n_iterations):
            rl_fmqa.optimize_step(beta)
        
        return rl_fmqa.get_optimization_summary()
    
    def random_beta_method(problem_func, problem_dim, n_iterations, n_initial):
        rl_fmqa = RLEnhancedFMQA(problem_dim, problem_func)
        rl_fmqa.initialize_dataset(n_initial)
        
        rng = np.random.default_rng(42)
        for _ in range(n_iterations):
            beta = rng.uniform(0.0, 2.0)
            rl_fmqa.optimize_step(beta)
        
        return rl_fmqa.get_optimization_summary()
    
    methods = {
        'Fixed-Beta-0.5': lambda **kwargs: fixed_beta_method(beta=0.5, **kwargs),
        'Fixed-Beta-1.0': lambda **kwargs: fixed_beta_method(beta=1.0, **kwargs),
        'Random-Beta': random_beta_method
    }
    
    # Run comparison
    results_df = evaluator.compare_methods(
        methods=methods,
        problems=problems,
        problem_dims=[50],
        n_iterations=20,
        n_runs=3,
        n_initial=5
    )
    
    # Generate plots
    evaluator.plot_convergence_comparison(results_df, save_path="example_results/convergence.png")
    evaluator.plot_performance_comparison(results_df, save_path="example_results/performance.png")
    
    # Generate report
    report = evaluator.generate_report(results_df)
    print("Benchmark completed! Check example_results/ for detailed results.")
    
    return results_df, report


def main():
    """Run the complete example."""
    print("=" * 60)
    print("RL-QUBO Basic Example")
    print("=" * 60)
    
    # Create directories
    Path("models").mkdir(exist_ok=True)
    Path("example_results").mkdir(exist_ok=True)
    
    # Step 1: Train a simple RL agent
    print("\n1. Training RL Agent...")
    agent = train_simple_agent()
    
    # Step 2: Demonstrate basic RL-FMQA
    print("\n2. Demonstrating RL-FMQA with different strategies...")
    basic_results = demonstrate_rl_fmqa()
    
    # Step 3: Test with trained agent
    print("\n3. Testing with trained RL agent...")
    rl_result = compare_with_trained_agent()
    
    if rl_result:
        basic_results["RL Agent"] = rl_result
    
    # Step 4: Plot comparisons
    print("\n4. Generating comparison plots...")
    plot_optimization_comparison(basic_results)
    
    # Step 5: Run comprehensive benchmark
    print("\n5. Running comprehensive benchmark...")
    benchmark_df, benchmark_report = run_comprehensive_benchmark()
    
    print("\n" + "=" * 60)
    print("Example completed successfully!")
    print("Check the generated plots and results files.")
    print("=" * 60)


if __name__ == "__main__":
    main()
